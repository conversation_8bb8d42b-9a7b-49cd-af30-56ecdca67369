// HTTP代理工具
// 统一通过代理服务器发送RSS请求

const PROXY_URL = 'https://www.apiworker.fun';

/**
 * 通过代理发送RSS请求
 * @param {string} rssUrl - 需要代理的RSS URL
 * @param {object} options - 请求选项
 * @returns {Promise} - 返回Promise对象
 */
export function requestRSSViaProxy(rssUrl, options = {}) {
  return new Promise(function(resolve, reject) {
    // 检查网络状态
    wx.getNetworkType({
      success: function(res) {
        if (res.networkType === 'none') {
          reject(new Error('网络连接不可用，请检查网络设置'));
          return;
        }
      }
    });

    // 构建请求参数
    const requestOptions = {
      url: PROXY_URL,
      method: 'GET',
      header: {
        'rss_url': rssUrl,
        'Accept': 'application/rss+xml, application/xml, text/xml, */*',
        'Accept-Charset': 'utf-8'
      },
      dataType: 'text',
      timeout: options.timeout || 8000,
      success: function(res) {
        if (res.statusCode === 200) {
          let responseData = res.data;
          
          // 确保响应数据是字符串格式
          if (typeof responseData !== 'string') {
            responseData = String(responseData);
          }

          // 清理可能的乱码字符
          if (responseData.includes('�')) {
            responseData = responseData.replace(/�+/g, '');
          }

          resolve({
            ...res,
            data: responseData
          });
        } else {
          reject(new Error(`代理请求状态码异常: ${res.statusCode}`));
        }
      },
      fail: function(err) {
        reject(err);
      }
    };

    // 合并自定义选项
    if (options.header) {
      requestOptions.header = {
        ...requestOptions.header,
        ...options.header
      };
    }

    wx.request(requestOptions);
  });
}

/**
 * 简单验证RSS源（用于添加订阅源，更宽松的验证）
 * @param {string} rssUrl - RSS URL
 * @returns {Promise} - 返回Promise对象
 */
export function simpleValidateRSSViaProxy(rssUrl) {
  return new Promise(function(resolve, reject) {
    requestRSSViaProxy(rssUrl, {
      timeout: 10000
    }).then(function(response) {
      // 只检查状态码，不检查内容格式
      if (response.statusCode !== 200) {
        throw new Error(`请求失败，状态码: ${response.statusCode}`);
      }

      // 检查是否有响应数据
      if (!response.data) {
        throw new Error('响应数据为空');
      }

      resolve(response);
    }).catch(function(err) {
      reject(err);
    });
  });
}

/**
 * 验证RSS源是否有效（通过代理）
 * @param {string} rssUrl - RSS URL
 * @returns {Promise} - 返回Promise对象
 */
export function validateRSSViaProxy(rssUrl) {
  return new Promise(function(resolve, reject) {
    requestRSSViaProxy(rssUrl, {
      timeout: 10000
    }).then(function(response) {
      // 检查响应状态码
      if (response.statusCode !== 200) {
        throw new Error(`Invalid response status: ${response.statusCode}`);
      }

      // 检查响应内容是否为有效的RSS/Atom格式
      const responseData = response.data;
      
      if (typeof responseData === 'string') {
        // 更宽松的RSS/XML格式检查
        const lowerCaseData = responseData.toLowerCase();
        const isXml = lowerCaseData.includes('<?xml') ||
                      lowerCaseData.includes('<rss') ||
                      lowerCaseData.includes('<feed') ||
                      lowerCaseData.includes('<channel') ||
                      lowerCaseData.includes('<item') ||
                      lowerCaseData.includes('<entry');

        if (!isXml) {
          throw new Error('无效的RSS格式');
        }
      } else if (typeof responseData === 'object') {
        if (responseData && responseData.error) {
          throw new Error(responseData.error);
        }
        // 对于JSON格式的响应，尝试更宽松的检查
        if (responseData && (responseData.items || responseData.feed || responseData.channel || responseData.entries)) {
          // 这是有效的JSON RSS格式
        } else {
          // 不抛出错误，让后续的解析器处理
        }
      } else {
        throw new Error('未知的响应数据格式');
      }

      resolve(response);
    }).catch(function(err) {
      reject(err);
    });
  });
}

/**
 * 批量通过代理请求多个RSS源
 * @param {Array} rssUrls - RSS URL数组
 * @param {object} options - 请求选项
 * @returns {Promise} - 返回Promise对象
 */
export function batchRequestRSSViaProxy(rssUrls, options = {}) {
  const batchSize = options.batchSize || 3; // 默认每批3个
  const promises = [];

  // 将URLs分批处理
  for (let i = 0; i < rssUrls.length; i += batchSize) {
    const batch = rssUrls.slice(i, i + batchSize);
    const batchPromises = batch.map(function(url) {
      return requestRSSViaProxy(url, options).catch(function(err) {
        return { error: err, url: url };
      });
    });
    promises.push(Promise.all(batchPromises));
  }

  return Promise.all(promises).then(function(batchResults) {
    // 展平结果数组
    return batchResults.flat();
  });
}

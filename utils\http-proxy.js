// HTTP代理工具
// 统一通过代理服务器发送RSS请求

const PROXY_URL = 'https://www.apiworker.fun';

/**
 * 通过代理发送RSS请求
 * @param {string} rssUrl - 需要代理的RSS URL
 * @param {object} options - 请求选项
 * @returns {Promise} - 返回Promise对象
 */
export function requestRSSViaProxy(rssUrl, options = {}) {
  return new Promise(function(resolve, reject) {
    // 检查网络状态
    wx.getNetworkType({
      success: function(res) {
        if (res.networkType === 'none') {
          reject(new Error('网络连接不可用，请检查网络设置'));
          return;
        }
      }
    });

    // 构建请求参数 - 尝试多种方式禁用压缩
    const requestOptions = {
      url: PROXY_URL,
      method: 'GET',
      header: {
        'rss_url': rssUrl,
        'Accept': 'application/rss+xml, application/xml, text/xml, text/plain, */*',
        'Accept-Charset': 'utf-8',
        'Accept-Encoding': 'identity', // 禁用压缩方式1
        'Content-Type': 'application/xml; charset=utf-8',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        // 尝试多种方式禁用压缩
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      },
      dataType: 'text', // 强制以文本形式接收
      enableHttp2: false, // 禁用HTTP2
      enableQuic: false, // 禁用QUIC
      timeout: options.timeout || 8000,
      success: function(res) {
        console.log('代理请求成功:', rssUrl, 'Status:', res.statusCode);
        console.log('响应头:', res.header);

        if (res.statusCode === 200) {
          let responseData = res.data;
          const contentType = res.header['content-type'] || res.header['Content-Type'] || '';

          console.log('响应Content-Type:', contentType);
          console.log('原始响应数据类型:', typeof responseData);

          // 检查是否收到了gzip压缩数据
          if (contentType.includes('application/x-gzip') || contentType.includes('gzip')) {
            console.error('收到gzip压缩数据，但小程序无法正确解压');
            reject(new Error('服务器返回了压缩数据，请检查Accept-Encoding头部设置'));
            return;
          }

          // 确保响应数据是字符串格式
          if (typeof responseData !== 'string') {
            console.warn('响应数据不是字符串格式，尝试转换');
            responseData = String(responseData);
          }

          console.log('响应数据长度:', responseData.length);
          console.log('响应数据前200字符:', responseData.substring(0, 200));

          // 检查是否仍有乱码问题
          if (responseData.includes('�')) {
            console.warn('代理服务器返回的数据仍包含乱码字符');
            // 记录乱码位置用于调试
            const invalidCharPositions = [];
            for (let i = 0; i < Math.min(responseData.length, 1000); i++) {
              if (responseData[i] === '�') {
                invalidCharPositions.push(i);
              }
            }
            console.log('乱码字符位置:', invalidCharPositions.slice(0, 10));

            // 尝试清理乱码字符
            responseData = responseData.replace(/�+/g, '');
            console.log('清理后数据长度:', responseData.length);
          }

          // 验证XML格式
          if (!responseData.trim().startsWith('<?xml') && !responseData.trim().startsWith('<rss') && !responseData.trim().startsWith('<feed')) {
            console.warn('响应数据可能不是有效的XML格式');
            console.log('数据开头:', responseData.substring(0, 100));
          }

          // 返回处理后的响应
          resolve({
            ...res,
            data: responseData
          });
        } else {
          reject(new Error(`代理请求状态码异常: ${res.statusCode}`));
        }
      },
      fail: function(err) {
        console.error('代理请求失败:', rssUrl, err);
        console.log('尝试备用请求方法...');

        // 备用请求：使用更简单的头部配置
        wx.request({
          url: PROXY_URL,
          method: 'GET',
          header: {
            'rss_url': rssUrl,
            'Accept': 'text/xml, application/xml, */*',
            'Accept-Encoding': 'identity'
          },
          dataType: 'text',
          timeout: options.timeout || 8000,
          success: function(retryRes) {
            console.log('备用请求成功');
            resolve(retryRes);
          },
          fail: function(retryErr) {
            console.error('备用请求也失败:', retryErr);
            reject(err); // 返回原始错误
          }
        });
      }
    };

    // 合并自定义选项
    if (options.header) {
      requestOptions.header = {
        ...requestOptions.header,
        ...options.header
      };
    }

    // 打印完整的请求信息用于调试
    console.log('=== RSS代理请求详情 ===');
    console.log('请求URL:', requestOptions.url);
    console.log('请求方法:', requestOptions.method);
    console.log('请求头部:', JSON.stringify(requestOptions.header, null, 2));
    console.log('dataType:', requestOptions.dataType);
    console.log('enableHttp2:', requestOptions.enableHttp2);
    console.log('enableQuic:', requestOptions.enableQuic);
    console.log('========================');

    // 发送请求
    wx.request(requestOptions);
  });
}

/**
 * 简单验证RSS源（用于添加订阅源，更宽松的验证）
 * @param {string} rssUrl - RSS URL
 * @returns {Promise} - 返回Promise对象
 */
export function simpleValidateRSSViaProxy(rssUrl) {
  return new Promise(function(resolve, reject) {
    // 优先使用无压缩请求
    requestRSSWithoutCompression(rssUrl, {
      timeout: 10000
    }).then(function(response) {
      // 只检查状态码，不检查内容格式
      if (response.statusCode !== 200) {
        throw new Error(`请求失败，状态码: ${response.statusCode}`);
      }

      // 检查是否有响应数据
      if (!response.data) {
        throw new Error('响应数据为空');
      }

      console.log('RSS源验证通过:', rssUrl);
      resolve(response);
    }).catch(function(err) {
      reject(err);
    });
  });
}

/**
 * 验证RSS源是否有效（通过代理）
 * @param {string} rssUrl - RSS URL
 * @returns {Promise} - 返回Promise对象
 */
export function validateRSSViaProxy(rssUrl) {
  return new Promise(function(resolve, reject) {
    requestRSSViaProxy(rssUrl, {
      timeout: 10000 // 验证时使用较短的超时时间
    }).then(function(response) {
      // 检查响应状态码
      if (response.statusCode !== 200) {
        throw new Error(`Invalid response status: ${response.statusCode}`);
      }

      // 检查响应内容是否为有效的RSS/Atom格式
      const responseData = response.data;
      console.log('代理返回的数据类型:', typeof responseData);
      console.log('代理返回的数据长度:', responseData ? responseData.length : 0);
      console.log('代理返回的数据前200字符:', responseData ? responseData.substring(0, 200) : 'null');

      if (typeof responseData === 'string') {
        // 更宽松的RSS/XML格式检查
        const lowerCaseData = responseData.toLowerCase();
        const isXml = lowerCaseData.includes('<?xml') ||
                      lowerCaseData.includes('<rss') ||
                      lowerCaseData.includes('<feed') ||
                      lowerCaseData.includes('<channel') ||
                      lowerCaseData.includes('<item') ||
                      lowerCaseData.includes('<entry');

        if (!isXml) {
          console.error('RSS格式验证失败，数据内容:', responseData.substring(0, 500));
          throw new Error('无效的RSS格式');
        }
      } else if (typeof responseData === 'object') {
        if (responseData && responseData.error) {
          throw new Error(responseData.error);
        }
        // 对于JSON格式的响应，尝试更宽松的检查
        if (responseData && (responseData.items || responseData.feed || responseData.channel || responseData.entries)) {
          // 这是有效的JSON RSS格式
          console.log('检测到JSON格式的RSS数据');
        } else {
          console.log('未知的JSON响应格式:', responseData);
          // 不抛出错误，让后续的解析器处理
        }
      } else {
        console.error('未知的响应数据类型:', typeof responseData);
        throw new Error('未知的响应数据格式');
      }

      resolve(response);
    }).catch(function(err) {
      reject(err);
    });
  });
}

/**
 * 批量通过代理请求多个RSS源
 * @param {Array} rssUrls - RSS URL数组
 * @param {object} options - 请求选项
 * @returns {Promise} - 返回Promise对象
 */
export function batchRequestRSSViaProxy(rssUrls, options = {}) {
  const batchSize = options.batchSize || 3; // 默认每批3个
  const promises = [];

  // 将URLs分批处理
  for (let i = 0; i < rssUrls.length; i += batchSize) {
    const batch = rssUrls.slice(i, i + batchSize);
    const batchPromises = batch.map(function(url) {
      return requestRSSViaProxy(url, options).catch(function(err) {
        return { error: err, url: url };
      });
    });
    promises.push(Promise.all(batchPromises));
  }

  return Promise.all(promises).then(function(batchResults) {
    // 展平结果数组
    return batchResults.flat();
  });
}

/**
 * 强制无压缩的RSS请求
 * @param {string} rssUrl - RSS URL
 * @param {object} options - 请求选项
 * @returns {Promise} - 返回Promise对象
 */
export function requestRSSWithoutCompression(rssUrl, options = {}) {
  return new Promise(function(resolve, reject) {
    console.log('发起无压缩RSS请求:', rssUrl);

    // 尝试多种不同的请求配置
    const requestConfigs = [
      // 配置1: 最简单的头部
      {
        url: PROXY_URL,
        method: 'GET',
        header: {
          'rss_url': rssUrl
        },
        dataType: 'text',
        timeout: options.timeout || 8000
      },
      // 配置2: 明确禁用压缩
      {
        url: PROXY_URL,
        method: 'GET',
        header: {
          'rss_url': rssUrl,
          'Accept-Encoding': ''  // 空字符串
        },
        dataType: 'text',
        timeout: options.timeout || 8000
      },
      // 配置3: 使用不同的Accept头部
      {
        url: PROXY_URL,
        method: 'GET',
        header: {
          'rss_url': rssUrl,
          'Accept': 'text/xml',
          'Accept-Encoding': 'identity'
        },
        dataType: 'text',
        timeout: options.timeout || 8000
      }
    ];

    let currentConfigIndex = 0;

    function tryRequest() {
      if (currentConfigIndex >= requestConfigs.length) {
        reject(new Error('所有请求配置都失败了'));
        return;
      }

      const config = requestConfigs[currentConfigIndex];
      console.log(`尝试配置 ${currentConfigIndex + 1}:`, JSON.stringify(config.header, null, 2));

      wx.request({
        ...config,
        success: function(res) {
          const contentType = res.header['content-type'] || res.header['Content-Type'] || '';
          console.log(`配置 ${currentConfigIndex + 1} 响应:`, {
            statusCode: res.statusCode,
            contentType: contentType,
            dataType: typeof res.data,
            dataLength: res.data ? res.data.length : 0
          });

          if (res.statusCode === 200 && !contentType.includes('gzip')) {
            console.log(`配置 ${currentConfigIndex + 1} 成功！`);
            resolve(res);
          } else {
            console.log(`配置 ${currentConfigIndex + 1} 失败，尝试下一个配置`);
            currentConfigIndex++;
            tryRequest();
          }
        },
        fail: function(err) {
          console.log(`配置 ${currentConfigIndex + 1} 请求失败:`, err);
          currentConfigIndex++;
          tryRequest();
        }
      });
    }

    tryRequest();
  });
}

/**
 * 测试RSS代理请求，用于调试编码问题
 * @param {string} rssUrl - RSS URL
 * @returns {Promise} - 返回Promise对象
 */
export function testRSSProxy(rssUrl) {
  return new Promise(function(resolve) {
    console.log('开始测试RSS代理请求:', rssUrl);

    // 先尝试无压缩请求
    requestRSSWithoutCompression(rssUrl, {
      timeout: 10000
    }).then(function(response) {
      const result = {
        success: true,
        statusCode: response.statusCode,
        contentType: response.header['content-type'] || response.header['Content-Type'],
        dataType: typeof response.data,
        dataLength: response.data ? response.data.length : 0,
        hasInvalidChars: response.data ? response.data.includes('�') : false,
        dataPreview: response.data ? response.data.substring(0, 300) : '',
        isValidXML: false
      };

      // 检查是否是有效的XML
      if (response.data && typeof response.data === 'string') {
        const trimmedData = response.data.trim();
        result.isValidXML = trimmedData.startsWith('<?xml') ||
                           trimmedData.startsWith('<rss') ||
                           trimmedData.startsWith('<feed');
      }

      console.log('RSS代理测试结果:', result);
      resolve(result);
    }).catch(function(err) {
      const result = {
        success: false,
        error: err.message || err.toString(),
        errorDetails: err
      };
      console.error('RSS代理测试失败:', result);
      resolve(result); // 不reject，返回错误信息
    });
  });
}

.detail-container {
  padding: 32rpx 20rpx;
  background: #f7f8fa;
  min-height: 100vh;
  width: 100%;
  box-sizing: border-box;
}

.detail-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #222;
  margin-bottom: 18rpx;
  line-height: 1.4;
}

.detail-meta {
  font-size: 24rpx;
  color: #888;
  margin-bottom: 10rpx;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.meta-dot {
  margin: 0 8rpx;
}

.detail-tags {
  margin-bottom: 18rpx;
}

.tag {
  display: inline-block;
  background: #e6f0fa;
  color: #409eff;
  border-radius: 20rpx;
  padding: 4rpx 18rpx;
  font-size: 22rpx;
  margin-right: 12rpx;
  margin-bottom: 6rpx;
}

.detail-content {
  background: #fff;
  border-radius: 12rpx;
  padding: 24rpx 18rpx;
  font-size: 28rpx;
  color: #333;
  line-height: 1.8;
  margin-bottom: 32rpx;
  word-break: break-all;
}

/* rich-text组件样式 */
.detail-content rich-text {
  width: 100%;
  display: block;
  line-height: 1.6;
  font-size: 32rpx;
}

/* 确保rich-text中的图片自适应屏幕宽度 */
.detail-content rich-text >>> img {
  max-width: 100% !important;
  width: auto !important;
  height: auto !important;
  display: block !important;
  margin: 20rpx auto !important;
  border-radius: 8rpx;
}

/* 链接样式 */
.detail-content rich-text >>> a {
  color: #007aff;
  text-decoration: underline;
}

/* 段落样式 */
.detail-content rich-text >>> p {
  margin: 16rpx 0;
  line-height: 1.6;
}

/* 列表样式 */
.detail-content rich-text >>> ul,
.detail-content rich-text >>> ol {
  margin: 16rpx 0;
  padding-left: 40rpx;
}

.detail-content rich-text >>> li {
  margin: 8rpx 0;
  line-height: 1.5;
}

.origin-btn {
  width: 100%;
  background: #409eff;
  color: #fff;
  border-radius: 24rpx;
  font-size: 28rpx;
  padding: 18rpx 0;
  margin-top: 10rpx;
}

.article-header {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #e0e0e0;
  margin-bottom: 20rpx;
}

.article-title {
  font-size: 36rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 16rpx;
  line-height: 1.4;
}

.article-meta {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #999999;
}

.article-content {
  padding: 20rpx 0;
  font-size: 30rpx;
  line-height: 1.6;
  color: #333333;
}

.article-content rich-text {
  display: block;
}

.article-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx;
  background: #ffffff;
  box-shadow: 0 -2rpx 4rpx rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #1976D2;
  color: #ffffff;
  border: none;
  border-radius: 8rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
}

.action-button .icon {
  margin-right: 8rpx;
  font-size: 32rpx;
}
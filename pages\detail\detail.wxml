<view class="detail-container">
  <view class="detail-title">{{article.title}}</view>
  <view class="detail-meta">
    <text>作者：{{article.author}}</text>
    <text class="meta-dot">·</text>
    <text>{{article.pubDate}}</text>
    <text class="meta-dot">·</text>
    <text>来源：{{article.source}}</text>
  </view>
  <view class="detail-tags">
    <block wx:for="{{article.category}}" wx:key="*this">
      <text class="tag">{{item}}</text>
    </block>
  </view>
  <view class="detail-content">
    <rich-text nodes="{{article.content || article.summary}}" space="nbsp"></rich-text>
  </view>
  <button class="origin-btn" bindtap="openInBrowser">阅读原文</button>
</view>
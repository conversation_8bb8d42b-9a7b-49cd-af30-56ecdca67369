<view class="container">
  <!-- 添加新订阅源 -->
  <view class="add-feed-section">
    <input class="input"
           placeholder="请输入RSS订阅源URL"
           value="{{newFeedUrl}}"
           bindinput="onFeedUrlInput"/>

    <input class="input"
           placeholder="自定义订阅源名称（可选）"
           value="{{newFeedName}}"
           bindinput="onFeedNameInput"/>

    <view class="category-input">
      <input class="input"
             placeholder="输入新分类或选择已有分类"
             value="{{newCategory}}"
             bindinput="onCategoryInput"/>
      <picker mode="selector"
              range="{{categories}}"
              bindchange="onCategorySelect">
        <view class="picker">
          <text>选择分类</text>
        </view>
      </picker>
    </view>

    <button class="primary-button"
            bindtap="addFeed"
            disabled="{{!newFeedUrl || !newCategory}}">
      添加订阅源
    </button>

    <button class="test-button" bindtap="testEncoding">测试编码问题</button>
  </view>

  <!-- 分类筛选栏 -->
  <scroll-view class="category-scroll" scroll-x="true">
    <view class="category-list">
      <view class="category-tag {{currentCategory === 'all' ? 'active' : ''}}" 
            bindtap="switchCategory" data-category="all">全部</view>
      <view class="category-tag {{currentCategory === item ? 'active' : ''}}" 
            wx:for="{{categories}}" wx:key="*this"
            bindtap="switchCategory" data-category="{{item}}">{{item}}</view>
    </view>
  </scroll-view>

  <!-- 订阅源列表 -->
  <view class="feeds-list">
    <view class="feed-item" wx:for="{{feeds}}" wx:key="url">
      <view class="feed-info">
        <text class="feed-title">{{item.title}}</text>
        <text class="feed-url">{{item.url}}</text>
        <text class="feed-category">分类：{{item.category}}</text>
      </view>
      <button class="update-feed-btn" data-url="{{item.url}}" bindtap="onUpdateFeed">更新</button>
      <button class="delete-feed-btn" data-url="{{item.url}}" bindtap="deleteFeed">删除</button>
    </view>
  </view>

  <!-- 分类管理 -->
  <view class="categories-section">
    <view class="section-title">分类管理</view>
    <view class="category-list">
      <view class="category-item" wx:for="{{categories}}" wx:key="*this">
        <text>{{item}}</text>
        <button class="delete-button" 
                size="mini" 
                bindtap="deleteCategory" 
                data-category="{{item}}">删除</button>
      </view>
    </view>
  </view>
</view>
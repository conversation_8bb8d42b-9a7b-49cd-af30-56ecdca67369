const app = getApp()
import { parseRSSFeed } from '../../utils/rss-parser'
import { simpleValidateRSSViaProxy, requestRSSViaProxy, testRSSProxy, requestRSSWithoutCompression } from '../../utils/http-proxy'

Page({
  data: {
    newFeedUrl: '',
    newFeedName: '',
    newCategory: '',
    feeds: [],
    categories: [],
    currentCategory: 'all'
  },

  onLoad() {
    this.loadFeeds()
    this.loadCategories()
  },

  onShow() {
    // 每次显示页面时重新加载数据
    this.loadFeeds()
    this.loadCategories()
  },

  loadFeeds() {
    const feeds = app.globalData.feeds
    this.setData({ feeds })
  },

  loadCategories() {
    const categories = app.globalData.categories
    this.setData({ categories })
  },

  onFeedUrlInput(e) {
    this.setData({
      newFeedUrl: e.detail.value
    })
  },

  onFeedNameInput(e) {
    this.setData({
      newFeedName: e.detail.value
    })
  },

  onCategoryInput(e) {
    this.setData({
      newCategory: e.detail.value
    })
  },

  onCategorySelect(e) {
    const index = e.detail.value
    this.setData({
      newCategory: this.data.categories[index]
    })
  },

  isValidUrl(urlString) {
    if (typeof urlString !== 'string' || urlString.trim() === '') {
      return false;
    }
    try {
      const url = new URL(urlString);
      // Ensure protocol is http or https
      return url.protocol === "http:" || url.protocol === "https:";
    } catch (e) {
      // If URL constructor fails, it's not a valid URL
      return false;
    }
  },

  // 标准化URL，便于比较
  normalizeUrl(url) {
    try {
      if (!url) return '';

      // 确保URL有协议前缀
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        url = 'https://' + url;
      }

      // 使用URL对象标准化URL
      const urlObj = new URL(url);

      // 移除URL末尾的斜杠
      let normalizedUrl = urlObj.origin + urlObj.pathname.replace(/\/$/, '');

      // 添加查询参数（如果有）
      if (urlObj.search) {
        normalizedUrl += urlObj.search;
      }

      return normalizedUrl.toLowerCase();
    } catch (e) {
      // 如果解析失败，返回原始URL
      return url.toLowerCase();
    }
  },

  async addFeed() {
    let { newFeedUrl, newFeedName, newCategory } = this.data;
    newFeedUrl = newFeedUrl.trim(); // Trim whitespace
    newFeedName = newFeedName.trim(); // Trim whitespace

    // 将HTTP转换为HTTPS
    if (newFeedUrl.startsWith('http://')) {
      newFeedUrl = newFeedUrl.replace('http://', 'https://');
    }

    // 标准化URL，便于比较
    const normalizedNewUrl = this.normalizeUrl(newFeedUrl);

    // 检查是否已存在相同的URL（使用标准化后的URL进行比较）
    if (this.data.feeds.some(feed => this.normalizeUrl(feed.url) === normalizedNewUrl)) {
      wx.showToast({
        title: '您已订阅该订阅源~',
        icon: 'none',
        duration: 2000
      })
      return
    }

    wx.showLoading({
      title: '正在验证加载订阅源...',
    })

    try {
      // 通过代理验证RSS源（使用宽松验证）
      console.log('开始验证RSS源:', newFeedUrl);
      const response = await simpleValidateRSSViaProxy(newFeedUrl)
      console.log('RSS验证成功，响应数据类型:', typeof response.data);

      // 解析RSS标题
      let title = newFeedUrl
      try {
        // 记录响应数据的详细信息用于调试
        console.log('RSS响应状态码:', response.statusCode);
        console.log('RSS响应数据类型:', typeof response.data);
        console.log('RSS响应数据长度:', response.data ? response.data.length : 0);

        if (response.data) {
          // 检查是否包含乱码
          if (typeof response.data === 'string' && response.data.includes('�')) {
            console.warn('响应数据包含乱码字符');
            wx.showModal({
              title: '数据编码问题',
              content: '检测到RSS数据可能存在编码问题，但仍可尝试添加订阅源',
              showCancel: false
            });
          }

          console.log('RSS响应数据预览:', response.data.substring(0, 500));

          // 使用正则表达式提取标题
          if (typeof response.data === 'string') {
            const titleMatch = response.data.match(/<title[^>]*>([^<]+)<\/title>/i)
            if (titleMatch && titleMatch[1]) {
              title = titleMatch[1].trim()
              // 清理可能的HTML实体
              title = title.replace(/&lt;/g, '<').replace(/&gt;/g, '>').replace(/&amp;/g, '&');
              console.log('提取到的RSS标题:', title);
            }
          }
        }
      } catch (parseError) {
        console.error('解析RSS标题失败:', parseError)
      }

      // 添加新订阅源
      const newFeed = {
        url: newFeedUrl,
        title: newFeedName || title, // 优先使用用户输入的名称
        displayName: newFeedName, // 保存用户输入的名称
        category: newCategory
      }

      // 更新全局数据
      const feeds = [...app.globalData.feeds, newFeed]
      app.globalData.feeds = feeds
      wx.setStorageSync('feeds', feeds)

      console.log('订阅源添加成功:', newFeed);
      console.log('当前订阅源总数:', feeds.length);
      console.log('已保存到本地存储');

      // 如果是新分类，添加到分类列表
      if (!this.data.categories.includes(newCategory)) {
        const categories = [...this.data.categories, newCategory]
        app.globalData.categories = categories
        wx.setStorageSync('categories', categories)
      }

      // 更新页面数据
      this.setData({
        feeds,
        categories: app.globalData.categories,
        newFeedUrl: '',
        newFeedName: '',
        newCategory: ''
      })

      wx.showToast({
        title: '添加成功',
        icon: 'success'
      })
    } catch (err) {
      console.error('添加订阅源失败:', err)
      let errorMessage = '添加失败，请检查URL'

      if (err.message) {
        if (err.message.includes('timeout') || err.message.includes('超时')) {
          errorMessage = '请求超时，请检查网络连接'
        } else if (err.message.includes('404')) {
          errorMessage = 'RSS源不存在(404)'
        } else if (err.message.includes('500')) {
          errorMessage = '服务器错误(500)'
        } else if (err.message.includes('网络')) {
          errorMessage = '网络连接失败'
        } else if (err.message.includes('状态码')) {
          errorMessage = '服务器响应异常: ' + err.message
        } else {
          errorMessage = err.message
        }
      }

      wx.showModal({
        title: '添加订阅源失败',
        content: errorMessage + '\n\n提示：请确认RSS链接是否正确，或稍后重试',
        showCancel: false,
        confirmText: '确定'
      })
    } finally {
      wx.hideLoading()
    }
  },

  deleteFeed(e) {
    const url = e.currentTarget.dataset.url
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个订阅源吗？',
      success: (res) => {
        if (res.confirm) {
          const feeds = this.data.feeds.filter(feed => feed.url !== url)
          app.globalData.feeds = feeds
          wx.setStorageSync('feeds', feeds)
          this.setData({ feeds })
        }
      }
    })
  },

  deleteCategory(e) {
    const category = e.currentTarget.dataset.category
    
    // 检查该分类下是否还有订阅源
    const hasFeeds = this.data.feeds.some(feed => feed.category === category)
    if (hasFeeds) {
      wx.showToast({
        title: '请先删除该分类下的订阅源',
        icon: 'none'
      })
      return
    }

    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个分类吗？',
      success: (res) => {
        if (res.confirm) {
          const categories = this.data.categories.filter(cat => cat !== category)
          app.globalData.categories = categories
          wx.setStorageSync('categories', categories)
          this.setData({ categories })
        }
      }
    })
  },

  isValidUrl(url) {
    try {
      new URL(url)
      return true
    } catch {
      return false
    }
  },

  // 分类筛选
  switchCategory(e) {
    const category = e.currentTarget.dataset.category
    this.setData({ currentCategory: category })
  },

  // 更新订阅源
  async onUpdateFeed(e) {
    const url = e.currentTarget.dataset.url
    wx.showLoading({ title: '正在更新...' })

    try {
      // 通过无压缩代理请求RSS内容
      const response = await requestRSSWithoutCompression(url)

      // 解析RSS内容
      const articles = parseRSSFeed(response.data)

      // 存储到本地缓存，key 以 feed url 作为唯一标识
      const cacheKey = 'feed_articles_' + url
      const cacheData = {
        articles,
        timestamp: Date.now(),
        url
      }
      wx.setStorageSync(cacheKey, cacheData)

      wx.showToast({
        title: '更新成功',
        icon: 'success'
      })
    } catch (err) {
      console.error('更新订阅源失败:', err)
      wx.showToast({
        title: '更新失败',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
    }
  },

  // 测试编码问题
  async testEncoding() {
    const testUrl = 'https://rss.keepdev.fun/rss/bilibili/user/dynamic/289706107';

    wx.showLoading({
      title: '测试中...'
    });

    try {
      const result = await testRSSProxy(testUrl);

      wx.hideLoading();

      const message = result.success
        ? `测试成功！
状态码: ${result.statusCode}
Content-Type: ${result.contentType}
数据长度: ${result.dataLength}
是否有乱码: ${result.hasInvalidChars ? '是' : '否'}
是否有效XML: ${result.isValidXML ? '是' : '否'}`
        : `测试失败: ${result.error}`;

      wx.showModal({
        title: '编码测试结果',
        content: message,
        showCancel: false,
        confirmText: '确定'
      });

      console.log('完整测试结果:', result);
    } catch (err) {
      wx.hideLoading();
      wx.showModal({
        title: '测试失败',
        content: err.message || '未知错误',
        showCancel: false,
        confirmText: '确定'
      });
    }
  },

  // 测试无压缩请求
  async testNoCompression() {
    const testUrl = 'https://rss.keepdev.fun/rss/bilibili/user/dynamic/289706107';

    wx.showLoading({
      title: '测试无压缩请求...'
    });

    try {
      const result = await requestRSSWithoutCompression(testUrl, {
        timeout: 10000
      });

      wx.hideLoading();

      const contentType = result.header['content-type'] || result.header['Content-Type'] || '';
      const hasGzip = contentType.includes('gzip');

      const message = `无压缩请求测试结果：
状态码: ${result.statusCode}
Content-Type: ${contentType}
数据长度: ${result.data ? result.data.length : 0}
是否包含gzip: ${hasGzip ? '是' : '否'}
数据预览: ${result.data ? result.data.substring(0, 100) : '无数据'}`;

      wx.showModal({
        title: hasGzip ? '仍有压缩问题' : '无压缩请求成功',
        content: message,
        showCancel: false,
        confirmText: '确定'
      });

      console.log('无压缩请求完整结果:', result);
    } catch (err) {
      wx.hideLoading();
      wx.showModal({
        title: '无压缩请求失败',
        content: err.message || '所有配置都失败了',
        showCancel: false,
        confirmText: '确定'
      });
      console.error('无压缩请求错误:', err);
    }
  }
})
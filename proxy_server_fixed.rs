use axum::{
    body::to_bytes,
    extract::{Request, State},
    http::{header, HeaderMap, HeaderValue, Method, StatusCode, Uri},
    response::{IntoResponse, Response},
    routing::get,
    Router,
};
use http::response::Builder as ResponseBuilder;
use std::net::SocketAddr;
use tokio::net::TcpListener;
use tracing::{info, Level};
use tracing_subscriber::FmtSubscriber;
use encoding_rs::{Encoding, UTF_8, GBK, GB18030};

// Browser-like headers to bypass Cloudflare
const BROWSER_HEADERS: [(&str, &str); 13] = [
    ("accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"),
    ("accept-language", "zh-CN,zh;q=0.9,en;q=0.8"), // 优先中文
    ("cache-control", "no-cache"),
    ("pragma", "no-cache"),
    ("sec-ch-ua", "\"Google Chrome\";v=\"119\", \"Chromium\";v=\"119\", \"Not?A_Brand\";v=\"24\""),
    ("sec-ch-ua-mobile", "?0"),
    ("sec-ch-ua-platform", "\"Windows\""),
    ("sec-fetch-dest", "document"),
    ("sec-fetch-mode", "navigate"),
    ("sec-fetch-site", "none"),
    ("sec-fetch-user", "?1"),
    ("upgrade-insecure-requests", "1"),
    ("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36"),
];

const BODY_SIZE_LIMIT: usize = 10 * 1024 * 1024; // 10 MB limit for request body

#[derive(Clone)]
struct AppState {
    client: reqwest::Client,
}

#[tokio::main]
async fn main() {
    // Initialize tracing
    let subscriber = FmtSubscriber::builder()
        .with_max_level(Level::INFO)
        .finish();
    tracing::subscriber::set_global_default(subscriber).expect("setting default subscriber failed");

    // Create reqwest client with connection pool and timeouts
    let client = reqwest::Client::builder()
        .pool_idle_timeout(std::time::Duration::from_secs(90))
        .timeout(std::time::Duration::from_secs(30))
        .tcp_keepalive(std::time::Duration::from_secs(60))
        .http2_keep_alive_interval(std::time::Duration::from_secs(20))
        .http2_keep_alive_timeout(std::time::Duration::from_secs(90))
        .http2_keep_alive_while_idle(true)
        .gzip(true) // 启用gzip解压
        .deflate(true) // 启用deflate解压
        .brotli(true) // 启用brotli解压
        .build()
        .expect("Failed to create HTTP client");

    let app_state = AppState { client };

    // Build our application with a route
    let app = Router::new()
        .route("/", get(proxy_handler))
        .with_state(app_state);

    // Run our app
    let addr = SocketAddr::from(([0, 0, 0, 0], 3000));
    let listener = TcpListener::bind(addr)
        .await
        .expect("Failed to bind to address");
    info!("Listening on {}", listener.local_addr().expect("Failed to get local address"));

    axum::serve(listener, app.into_make_service())
        .await
        .unwrap();
}

async fn proxy_handler(
    State(state): State<AppState>,
    axum_request: Request,
) -> Result<Response, AppError> {
    // Extract target URL from headers (支持多种头部名称)
    let target_url_header = axum_request
        .headers()
        .get("X-Target-URL")
        .or_else(|| axum_request.headers().get("rss_url"))
        .ok_or_else(|| AppError::BadRequest("Missing X-Target-URL or rss_url header".into()))?;

    let target_url_str = target_url_header
        .to_str()
        .map_err(|_| AppError::BadRequest("Invalid target URL header (not valid UTF-8)".into()))?;

    info!("Proxying request to: {}", target_url_str);

    // Parse target URL
    let target_uri: Uri = target_url_str
        .parse()
        .map_err(|e| AppError::BadRequest(format!("Invalid target URL format: {}", e)))?;

    // Extract method and original headers
    let method: Method = axum_request.method().clone();
    let original_headers: HeaderMap = axum_request.headers().clone();

    // Build request to target using reqwest
    let mut client_request_builder = state
        .client
        .request(method.clone(), target_uri.to_string())
        .headers(build_headers(&original_headers));

    // Forward the request body if present
    let body_bytes = to_bytes(axum_request.into_body(), BODY_SIZE_LIMIT)
        .await
        .map_err(|e| {
            if e.to_string().to_lowercase().contains("body limit exceeded") ||
               e.to_string().to_lowercase().contains("payload too large") ||
               e.to_string().to_lowercase().contains("length limit exceeded")
            {
                AppError::BadRequest("Request body too large".into())
            } else {
                AppError::InternalServerError(format!("Failed to read request body: {}", e))
            }
        })?;

    if !body_bytes.is_empty() {
        client_request_builder = client_request_builder.body(body_bytes);
    }

    // Send request to target
    let upstream_response = client_request_builder
        .send()
        .await
        .map_err(|e| AppError::UpstreamError(format!("Upstream request failed: {}", e)))?;

    // Build axum response
    let status_code = upstream_response.status();
    let mut response_builder = ResponseBuilder::new().status(status_code);

    // Copy headers from target response, but handle encoding specially
    if let Some(headers_mut) = response_builder.headers_mut() {
        headers_mut.clear();
        for (key, value) in upstream_response.headers().iter() {
            // 确保Content-Type头部包含正确的编码信息
            if key == header::CONTENT_TYPE {
                let content_type_str = value.to_str().unwrap_or("application/xml");
                let enhanced_content_type = if content_type_str.contains("charset") {
                    content_type_str.to_string()
                } else {
                    format!("{}; charset=utf-8", content_type_str)
                };
                if let Ok(enhanced_value) = HeaderValue::from_str(&enhanced_content_type) {
                    headers_mut.insert(key.clone(), enhanced_value);
                } else {
                    headers_mut.insert(key.clone(), value.clone());
                }
            } else {
                headers_mut.append(key.clone(), value.clone());
            }
        }
        
        // 确保有正确的编码相关头部
        if !headers_mut.contains_key(header::CONTENT_TYPE) {
            headers_mut.insert(
                header::CONTENT_TYPE,
                HeaderValue::from_static("application/xml; charset=utf-8")
            );
        }
    }

    // Get the response body and handle encoding
    let response_body_bytes = upstream_response
        .bytes()
        .await
        .map_err(|e| AppError::InternalServerError(format!("Failed to read upstream response body: {}", e)))?;

    // 处理编码转换
    let processed_body = process_encoding(&response_body_bytes);

    // Create the axum response
    let final_response = response_builder
        .body(axum::body::Body::from(processed_body))
        .map_err(|e| AppError::InternalServerError(format!("Failed to create final response: {}", e)))?;

    Ok(final_response)
}

// 处理编码转换的函数
fn process_encoding(body_bytes: &[u8]) -> Vec<u8> {
    // 尝试检测编码
    let detected_encoding = detect_encoding(body_bytes);
    
    info!("Detected encoding: {:?}", detected_encoding.name());
    
    // 如果已经是UTF-8，直接返回
    if detected_encoding == UTF_8 {
        return body_bytes.to_vec();
    }
    
    // 转换为UTF-8
    let (decoded_string, _, had_errors) = detected_encoding.decode(body_bytes);
    
    if had_errors {
        info!("Warning: Encoding conversion had errors");
    }
    
    // 返回UTF-8编码的字节
    decoded_string.as_bytes().to_vec()
}

// 检测文本编码
fn detect_encoding(body_bytes: &[u8]) -> &'static Encoding {
    // 检查BOM
    if body_bytes.starts_with(&[0xEF, 0xBB, 0xBF]) {
        return UTF_8;
    }
    
    // 检查XML声明中的编码
    if let Ok(text_start) = std::str::from_utf8(&body_bytes[..std::cmp::min(200, body_bytes.len())]) {
        if let Some(encoding_match) = extract_xml_encoding(text_start) {
            match encoding_match.to_lowercase().as_str() {
                "utf-8" | "utf8" => return UTF_8,
                "gbk" => return GBK,
                "gb18030" => return GB18030,
                "gb2312" => return GBK, // GB2312是GBK的子集
                _ => {}
            }
        }
    }
    
    // 尝试UTF-8解码
    if std::str::from_utf8(body_bytes).is_ok() {
        return UTF_8;
    }
    
    // 检测是否可能是GBK/GB18030
    if is_likely_gbk(body_bytes) {
        return GBK;
    }
    
    // 默认假设为UTF-8
    UTF_8
}

// 从XML声明中提取编码信息
fn extract_xml_encoding(text: &str) -> Option<String> {
    if let Some(start) = text.find("encoding=") {
        let encoding_part = &text[start + 9..];
        if let Some(quote_start) = encoding_part.find('"').or_else(|| encoding_part.find('\'')) {
            let quote_char = encoding_part.chars().nth(quote_start).unwrap();
            let encoding_value = &encoding_part[quote_start + 1..];
            if let Some(quote_end) = encoding_value.find(quote_char) {
                return Some(encoding_value[..quote_end].to_string());
            }
        }
    }
    None
}

// 简单的GBK检测
fn is_likely_gbk(bytes: &[u8]) -> bool {
    let mut gbk_score = 0;
    let mut i = 0;
    
    while i < bytes.len() {
        if bytes[i] >= 0x81 && bytes[i] <= 0xFE && i + 1 < bytes.len() {
            let second = bytes[i + 1];
            if (second >= 0x40 && second <= 0x7E) || (second >= 0x80 && second <= 0xFE) {
                gbk_score += 1;
                i += 2;
            } else {
                i += 1;
            }
        } else {
            i += 1;
        }
    }
    
    // 如果有足够多的GBK字符模式，认为是GBK编码
    gbk_score > 5
}

fn build_headers(original_headers: &HeaderMap) -> HeaderMap {
    let mut new_headers = HeaderMap::new();

    // 添加浏览器头部
    for (key_str, value_str) in BROWSER_HEADERS.iter() {
        let header_name = header::HeaderName::from_static(key_str);
        let header_value = HeaderValue::from_static(value_str);
        new_headers.insert(header_name, header_value);
    }

    // 保留重要的原始头部
    let preserve_header_names_const = [
        header::ACCEPT_ENCODING,
        header::ACCEPT_LANGUAGE,
        header::REFERER,
        header::COOKIE,
        header::AUTHORIZATION,
        header::CONTENT_TYPE,
        header::ACCEPT_CHARSET,
    ];

    for header_name_const in preserve_header_names_const {
        if let Some(value) = original_headers.get(&header_name_const) {
            new_headers.insert(header_name_const.clone(), value.clone());
        }
    }
    
    // 确保有Accept-Charset头部
    if !new_headers.contains_key(header::ACCEPT_CHARSET) {
        new_headers.insert(
            header::ACCEPT_CHARSET,
            HeaderValue::from_static("utf-8, gbk, gb2312, gb18030, *")
        );
    }
    
    new_headers
}

#[derive(Debug)]
enum AppError {
    BadRequest(String),
    InternalServerError(String),
    UpstreamError(String),
}

impl IntoResponse for AppError {
    fn into_response(self) -> Response {
        let (status_code, error_message) = match self {
            AppError::BadRequest(msg) => (StatusCode::BAD_REQUEST, msg),
            AppError::InternalServerError(msg) => (StatusCode::INTERNAL_SERVER_ERROR, msg),
            AppError::UpstreamError(msg) => (StatusCode::BAD_GATEWAY, msg),
        };

        info!("Responding with error: {} - {}", status_code, error_message);
        let body = axum::Json(serde_json::json!({ "error": error_message }));
        (status_code, body).into_response()
    }
}

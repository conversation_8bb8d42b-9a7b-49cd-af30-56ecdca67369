// 测试RSS解析器
import { parseRSSFeed } from './utils/rss-parser.js';

// 测试数据 - 您提供的RSS 2.0格式
const testRSSData = `<?xml version="1.0" encoding="UTF-8" ?>
<rss version="2.0">
<channel>
  <title><![CDATA[淘沙博士 的 bilibili 动态]]></title>
  <link>https:&#x2F;&#x2F;space.bilibili.com&#x2F;289706107&#x2F;dynamic</link>
  <description>淘沙博士 的 bilibili 动态</description>
  <language>zh-cn</language>
  <category></category>
    <item>
        <title><![CDATA[伊朗发动反击！击落以色列F35？一眼假！]]></title>
        <link>https:&#x2F;&#x2F;t.bilibili.com&#x2F;1078238100759511043</link>
        <description><![CDATA[伊朗发动反击！击落以色列F35？一眼假！<br/><img src="http://i2.hdslb.com/bfs/archive/49d66520338a763fed1e2ebad58bb0f5ca932f03.jpg"/>]]></description>
        <pubDate>Sat, 14 Jun 2025 07:52:16 GMT</pubDate>
        <guid>https:&#x2F;&#x2F;t.bilibili.com&#x2F;1078238100759511043</guid>
        <author>淘沙博士</author>
        <category>av</category>
    </item>
    <item>
        <title><![CDATA[三哥真的破大防了！

在X平台上，有人发起一个投票：非法的墨西哥移民，和合法的印度移民，你会选择哪个？

结果有37000多人投票，83.6%选择非法老墨，只有16.4%选择合法三哥。

帖子底下，一群印度人破防了，疯狂发泄，哈哈哈～[吃瓜]]]></title>
        <link>https:&#x2F;&#x2F;t.bilibili.com&#x2F;1077207969996734481</link>
        <description><![CDATA[三哥真的破大防了！

在X平台上，有人发起一个投票：非法的墨西哥移民，和合法的印度移民，你会选择哪个？

结果有37000多人投票，83.6%选择非法老墨，只有16.4%选择合法三哥。

帖子底下，一群印度人破防了，疯狂发泄，哈哈哈～[吃瓜]<br/><img src="http://i0.hdslb.com/bfs/new_dyn/dbd97123304d7e229d84600eaea5815d289706107.jpg"/><br/>]]></description>
        <pubDate>Wed, 11 Jun 2025 13:52:16 GMT</pubDate>
        <guid>https:&#x2F;&#x2F;t.bilibili.com&#x2F;1077207969996734481</guid>
        <author>淘沙博士</author>
        <category>draw</category>
    </item>
</channel>
</rss>`;

// 执行测试
console.log('开始测试RSS解析器...');

const articles = parseRSSFeed(testRSSData);

console.log(`解析结果: 共${articles.length}篇文章`);

articles.forEach((article, index) => {
  console.log(`\n文章 ${index + 1}:`);
  console.log('标题:', article.title);
  console.log('链接:', article.link);
  console.log('作者:', article.author);
  console.log('发布时间:', article.pubDate);
  console.log('内容预览:', article.content ? article.content.substring(0, 100) + '...' : '无内容');
  console.log('是否包含图片:', article.content ? article.content.includes('<img') : false);
  console.log('链接是否正确解码:', article.link ? article.link.includes('/') : false);
});

console.log('\n测试完成！');

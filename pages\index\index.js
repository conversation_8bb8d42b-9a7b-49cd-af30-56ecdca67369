const app = getApp()
import { parseRSSFeed, formatDate } from '../../utils/rss-parser'
import { deleteFeed, updateFeedContent } from '../../utils/feed-manager'
import { getAllFavorites, isFavorite, addFavorite, removeFavorite } from '../../utils/favorite-manager'
import { requestRSSViaProxy } from '../../utils/http-proxy'

Page({
  data: {
    isLoggedIn: false,
    userInfo: null,
    categories: [], // 原始分类数据
    currentCategory: 'all',
    articles: [],
    page: 1,
    loading: false,
    categoryRange: ['全部'], // 用于picker的分类数组
    categoryIndex: 0 // 当前选中的分类索引
  },

  onLoad() {
    this.checkLoginStatus()
    this.loadCategories()
    this.loadArticles()
  },

  onShow() {
    // 每次显示首页时都同步最新feeds
    let feeds = wx.getStorageSync('feeds') || []
    if (feeds.length === 0 && app.globalData.feeds) {
      feeds = app.globalData.feeds
    }
    app.globalData.feeds = feeds

    console.log('首页onShow - 订阅源数量:', feeds.length);
    console.log('首页onShow - 订阅源列表:', feeds.map(f => ({ url: f.url, title: f.title })));

    this.loadCategories() // <--- 添加这行来刷新分类
    this.setData({
      page: 1,
      articles: []
    })
    this.loadArticles()
  },

  onPullDownRefresh() {
    this.setData({
      page: 1,
      articles: []
    })
    this.loadArticles().then(() => {
      wx.stopPullDownRefresh()
    })
  },

  async checkLoginStatus() {
    const isLoggedIn = await app.checkLoginStatus()
    this.setData({
      isLoggedIn,
      userInfo: app.globalData.userInfo
    })
  },

  async handleLogin() {
    try {
      const userInfo = await app.login()
      this.setData({
        isLoggedIn: true,
        userInfo
      })
    } catch (err) {
      wx.showToast({
        title: '登录失败',
        icon: 'none'
      })
    }
  },

  loadCategories() {
    const categories = app.globalData.categories || [];
    const categoryRange = ['全部', ...categories.map(cat => cat.name || cat)]; // 假设categories是对象数组或字符串数组
    let currentCategory = this.data.currentCategory;
    let categoryIndex = categoryRange.indexOf(currentCategory);
    if (categoryIndex === -1) { // 如果当前分类不在列表中，默认选中“全部”
        currentCategory = '全部';
        categoryIndex = 0;
    }

    this.setData({
      categories,
      categoryRange,
      categoryIndex,
      currentCategory //确保currentCategory也更新
    });
  },

  async loadArticles() {
    if (this.data.loading) return

    this.setData({ loading: true })

    // 显示加载提示
    wx.showLoading({
      title: '正在加载...',
      mask: true
    })

    try {
      const feeds = app.globalData.feeds || []
      console.log('开始加载文章，订阅源数量:', feeds.length)

      if (feeds.length === 0) {
        console.log('没有订阅源')
        this.setData({
          articles: [],
          loading: false
        })
        wx.hideLoading()
        return
      }

      let articles = []
      // 根据当前分类筛选feeds
      const filteredFeeds = this.data.currentCategory === 'all' || this.data.currentCategory === '全部'
        ? feeds
        : feeds.filter(feed => feed.category === this.data.currentCategory)

      console.log('筛选后的订阅源数量:', filteredFeeds.length)

      // 获取每个feed的文章，优先本地缓存
      for (const feed of filteredFeeds) {
        try {
          console.log('处理订阅源:', feed.url)

          // 优先读取本地缓存
          const cacheKey = 'feed_articles_' + feed.url
          const cachedData = wx.getStorageSync(cacheKey)
          let feedArticles = []

          // 检查缓存是否有效：存在、有文章数据、有时间戳且未过期
          const CACHE_EXPIRY = 30 * 60 * 1000 // 30分钟，单位毫秒
          const isCacheValid = cachedData &&
                              cachedData.articles &&
                              Array.isArray(cachedData.articles) &&
                              cachedData.articles.length > 0 &&
                              cachedData.timestamp &&
                              (Date.now() - cachedData.timestamp) < CACHE_EXPIRY

          if (isCacheValid) {
            // 使用缓存数据
            console.log('使用缓存数据:', feed.url, '文章数量:', cachedData.articles.length)
            feedArticles = cachedData.articles
          } else {
            // 无缓存或缓存过期则通过代理请求
            console.log('通过代理从网络获取:', feed.url)
            try {
              const response = await requestRSSViaProxy(feed.url, {
                timeout: 10000
              })

              console.log('代理请求成功，开始解析RSS')
              feedArticles = parseRSSFeed(response.data)
              console.log('RSS解析成功，文章数量:', feedArticles.length)

              // 更新缓存，包含时间戳
              wx.setStorageSync(cacheKey, {
                articles: feedArticles,
                timestamp: Date.now(),
                url: feed.url
              })
            } catch (fetchError) {
              console.error('获取订阅源失败:', feed.url, fetchError)
              // 如果请求失败但有缓存，尝试使用过期的缓存
              if (cachedData && cachedData.articles && Array.isArray(cachedData.articles)) {
                console.log('使用过期缓存:', feed.url)
                feedArticles = cachedData.articles
              }
            }
          }

          // 处理文章数据
          const processedArticles = feedArticles.map(article => ({
            ...article,
            source: feed.displayName || feed.title || feed.url,
            pubDate: article.pubDate ? formatDate(article.pubDate) : '',
            isFavorite: isFavorite(article.guid || article.link || article.title)
          }))

          articles = articles.concat(processedArticles)
          console.log('当前总文章数量:', articles.length)

        } catch (err) {
          console.error('处理订阅源失败:', feed.url, err)
        }
      }

      // 按发布时间排序
      articles.sort((a, b) => {
        const dateA = new Date(a.pubDate || 0)
        const dateB = new Date(b.pubDate || 0)
        return dateB - dateA
      })

      console.log('最终文章数量:', articles.length)

      // 更新文章列表
      this.setData({
        articles: this.data.page === 1 ? articles : [...this.data.articles, ...articles],
        page: this.data.page + 1
      })

    } catch (err) {
      console.error('加载文章失败:', err)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
      wx.hideLoading()
    }
  },

  onCategoryChange(e) {
    const selectedIndex = e.detail.value;
    const selectedCategoryName = this.data.categoryRange[selectedIndex];
    let actualCategoryValue = selectedCategoryName;

    // 如果选中的是“全部”，则currentCategory设为'all'
    // 否则，从原始categories中查找对应的分类对象或直接使用名称
    if (selectedCategoryName === '全部') {
      actualCategoryValue = 'all';
    } else {
      // 如果categories是对象数组，需要根据name找到对应的value，这里假设直接用name
      // 如果您的categories是 [{id:1, name:'技术'}, {id:2, name:'生活'}] 这种形式
      // 您可能需要在这里根据 selectedCategoryName 找到对应的 category.id 或其他唯一标识
      // 为简化，此处我们假设分类值就是分类名，或者 'all' 代表所有
      const foundCategory = this.data.categories.find(cat => (cat.name || cat) === selectedCategoryName);
      if (foundCategory && typeof foundCategory === 'object' && foundCategory.id) {
        actualCategoryValue = foundCategory.id; // 或者其他您用作筛选的字段
      } else {
        actualCategoryValue = selectedCategoryName; // 直接使用名称
      }
    }

    this.setData({
      currentCategory: actualCategoryValue,
      categoryIndex: selectedIndex,
      page: 1,
      articles: []
    });
    this.loadArticles();
  },

  loadMore() {
    this.loadArticles()
  },

  goToDetail(e) {
    const article = e.currentTarget.dataset.article
    wx.navigateTo({
      url: `/pages/detail/detail?article=${encodeURIComponent(JSON.stringify(article))}`
    })
  },

  // 删除RSS源
  async handleDeleteFeed(e) {
    const { feedId } = e.currentTarget.dataset
    try {
      wx.showModal({
        title: '确认删除',
        content: '确定要删除这个RSS源吗？',
        async success(res) {
          if (res.confirm) {
            await deleteFeed(feedId)
            wx.showToast({
              title: '删除成功',
              icon: 'success'
            })
            // 重新加载文章列表
            this.setData({
              page: 1,
              articles: []
            })
            this.loadArticles()
          }
        }
      })
    } catch (err) {
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      })
    }
  },

  // 更新RSS源
  async handleUpdateFeed(e) {
    const { feedId } = e.currentTarget.dataset
    try {
      wx.showLoading({
        title: '更新中...'
      })
      await updateFeedContent(feedId)
      wx.hideLoading()
      wx.showToast({
        title: '更新成功',
        icon: 'success'
      })
      // 重新加载文章列表
      this.setData({
        page: 1,
        articles: []
      })
      this.loadArticles()
    } catch (err) {
      wx.hideLoading()
      wx.showToast({
        title: '更新失败',
        icon: 'none'
      })
    }
  },

  // 收藏/取消收藏
  handleToggleFavorite(e) {
    const { guid } = e.currentTarget.dataset;
    const articles = this.data.articles;
    const articleIndex = articles.findIndex(article => article.guid === guid);

    if (articleIndex > -1) {
      const articleToToggle = articles[articleIndex];
      const newIsFavoriteState = !articleToToggle.isFavorite;

      if (newIsFavoriteState) {
        // addFavorite应该处理将文章添加到收藏列表的逻辑
        // articleToToggle 是当前文章对象，其 isFavorite 状态即将改变
        addFavorite(articleToToggle);
      } else {
        removeFavorite(guid);
      }

      // 使用路径语法精确更新数组中特定元素的属性
      this.setData({
        [`articles[${articleIndex}].isFavorite`]: newIsFavoriteState
      });
    } else {
      console.warn('Article not found in articles list for guid:', guid);
    }
  }
})
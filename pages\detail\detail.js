Page({
  data: {
    article: null
  },

  onLoad(options) {
    if (options.article) {
      const article = JSON.parse(decodeURIComponent(options.article))
      article.content = article.content || article.summary || ''

      // 简单处理文章内容
      if (article.content) {
        // 确保图片能够自适应屏幕宽度
        article.content = article.content.replace(/<img([^>]*)>/gi, function(_match, attrs) {
          if (!attrs.includes('style=')) {
            attrs += ' style="max-width: 100%; height: auto; display: block; margin: 10rpx auto;"';
          }
          return `<img${attrs}>`;
        });
      }

      this.setData({ article })
    }
  },

  // 获取解码后的链接
  getDecodedLink() {
    let link = this.data.article.link || '';

    // 解码常见的HTML实体
    link = link.replace(/&amp;/g, '&')
               .replace(/&lt;/g, '<')
               .replace(/&gt;/g, '>')
               .replace(/&quot;/g, '"')
               .replace(/&#x27;/g, "'")
               .replace(/&#x2F;/g, '/')
               .replace(/&#(\d+);/g, (_, dec) => String.fromCharCode(dec));

    // 如果链接看起来不像URL，尝试进一步修复
    if (!link.match(/^https?:\/\//i)) {
      // 如果链接以//开头，添加https:
      if (link.startsWith('//')) {
        link = 'https:' + link;
      }
      // 如果链接不包含协议，添加https://
      else if (!link.includes('://')) {
        link = 'https://' + link;
      }
    }

    return link;
  },

  openInBrowser() {
    if (this.data.article && this.data.article.link) {
      // 获取解码后的链接
      const decodedLink = this.getDecodedLink();

      // 尝试使用web-view打开链接
      try {
        // 首先尝试直接打开网页
        wx.showActionSheet({
          itemList: ['在浏览器中打开', '复制链接'],
          success: (res) => {
            if (res.tapIndex === 0) {
              // 尝试使用系统浏览器打开
              wx.showLoading({ title: '正在打开...' });

              // 使用navigateTo打开web-view页面
              wx.navigateTo({
                url: `/pages/web-view/web-view?url=${encodeURIComponent(decodedLink)}`,
                fail: (err) => {
                  console.error('打开web-view失败:', err);
                  // 如果打开失败，回退到复制链接方式
                  this.copyLink();
                },
                complete: () => {
                  wx.hideLoading();
                }
              });
            } else if (res.tapIndex === 1) {
              // 复制链接
              this.copyLink();
            }
          }
        });
      } catch (err) {
        console.error('打开链接失败:', err);
        // 出错时回退到复制链接方式
        this.copyLink();
      }
    }
  },

  // 复制链接到剪贴板
  copyLink() {
    // 获取解码后的链接
    const link = this.getDecodedLink();
    console.log('解码后的链接:', link);

    wx.setClipboardData({
      data: link,
      success: () => {
        wx.showModal({
          title: '链接已复制',
          content: '由于小程序限制，请手动打开浏览器粘贴链接查看完整内容',
          showCancel: false
        });
      }
    });
  }
})
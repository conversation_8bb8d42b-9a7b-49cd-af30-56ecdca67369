Page({
  data: {
    article: null
  },

  onLoad(options) {
    if (options.article) {
      const article = JSON.parse(decodeURIComponent(options.article))
      article.content = article.content || article.summary || ''

      // 处理文章内容，确保在rich-text中正确显示
      if (article.content) {
        article.content = this.processContentForRichText(article.content);
      }

      // 获取屏幕宽度用于图片尺寸计算
      const systemInfo = wx.getSystemInfoSync();
      this.setData({
        article,
        screenWidth: systemInfo.screenWidth
      });

    } else {
      this.setData({ article });
    }
  },

  // 处理内容以适配rich-text组件
  processContentForRichText(content) {
    if (!content) return '';

    // 1. 确保图片能够自适应屏幕宽度
    content = content.replace(/<img([^>]*)>/gi, function(_match, attrs) {
      // 提取src属性
      const srcMatch = attrs.match(/src=["']([^"']*)["']/i);
      const src = srcMatch ? srcMatch[1] : '';

      // 提取alt属性
      const altMatch = attrs.match(/alt=["']([^"']*)["']/i);
      const alt = altMatch ? altMatch[1] : '';

      // 重新构建img标签，使用固定的样式确保不溢出
      // 使用calc()来确保图片宽度不超过容器
      const imageStyle = `
        max-width: calc(100vw - 80rpx);
        width: 100%;
        height: auto;
        display: block;
        margin: 20rpx auto;
        object-fit: contain;
        box-sizing: border-box;
        border-radius: 8rpx;
      `.replace(/\s+/g, ' ').trim();

      if (src) {
        return `<img src="${src}" ${alt ? `alt="${alt}"` : ''} style="${imageStyle}">`;
      } else {
        return `<img${attrs} style="${imageStyle}">`;
      }
    });

    // 2. 处理链接样式
    content = content.replace(/<a([^>]*)>/gi, function(_match, attrs) {
      if (!attrs.includes('style=')) {
        attrs += ' style="color: #007aff; text-decoration: underline;"';
      }
      return `<a${attrs}>`;
    });

    // 3. 处理段落样式
    content = content.replace(/<p([^>]*)>/gi, function(_match, attrs) {
      if (!attrs.includes('style=')) {
        attrs += ' style="margin: 16rpx 0; line-height: 1.6;"';
      }
      return `<p${attrs}>`;
    });

    // 4. 处理换行标签
    content = content.replace(/<br\s*\/?>/gi, '<br style="line-height: 1.6;">');

    return content;
  },

  // 获取解码后的链接
  getDecodedLink() {
    let link = this.data.article.link || '';

    // 解码常见的HTML实体
    link = link.replace(/&amp;/g, '&')
               .replace(/&lt;/g, '<')
               .replace(/&gt;/g, '>')
               .replace(/&quot;/g, '"')
               .replace(/&#x27;/g, "'")
               .replace(/&#x2F;/g, '/')
               .replace(/&#(\d+);/g, (_, dec) => String.fromCharCode(dec));

    // 如果链接看起来不像URL，尝试进一步修复
    if (!link.match(/^https?:\/\//i)) {
      // 如果链接以//开头，添加https:
      if (link.startsWith('//')) {
        link = 'https:' + link;
      }
      // 如果链接不包含协议，添加https://
      else if (!link.includes('://')) {
        link = 'https://' + link;
      }
    }

    return link;
  },

  openInBrowser() {
    if (this.data.article && this.data.article.link) {
      // 获取解码后的链接
      const decodedLink = this.getDecodedLink();

      // 尝试使用web-view打开链接
      try {
        // 首先尝试直接打开网页
        wx.showActionSheet({
          itemList: ['在浏览器中打开', '复制链接'],
          success: (res) => {
            if (res.tapIndex === 0) {
              // 尝试使用系统浏览器打开
              wx.showLoading({ title: '正在打开...' });

              // 使用navigateTo打开web-view页面
              wx.navigateTo({
                url: `/pages/web-view/web-view?url=${encodeURIComponent(decodedLink)}`,
                fail: (err) => {
                  console.error('打开web-view失败:', err);
                  // 如果打开失败，回退到复制链接方式
                  this.copyLink();
                },
                complete: () => {
                  wx.hideLoading();
                }
              });
            } else if (res.tapIndex === 1) {
              // 复制链接
              this.copyLink();
            }
          }
        });
      } catch (err) {
        console.error('打开链接失败:', err);
        // 出错时回退到复制链接方式
        this.copyLink();
      }
    }
  },

  // 复制链接到剪贴板
  copyLink() {
    // 获取解码后的链接
    const link = this.getDecodedLink();
    console.log('解码后的链接:', link);

    wx.setClipboardData({
      data: link,
      success: () => {
        wx.showModal({
          title: '链接已复制',
          content: '由于小程序限制，请手动打开浏览器粘贴链接查看完整内容',
          showCancel: false
        });
      }
    });
  }
})
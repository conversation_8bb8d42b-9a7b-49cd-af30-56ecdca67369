App({
  globalData: {
    userInfo: null,
    isLoggedIn: false,
    feeds: [],
    categories: []
  },

  onLaunch() {
    console.log('App启动');

    // 检查登录状态
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo) {
      this.globalData.userInfo = userInfo
      this.globalData.isLoggedIn = true
      console.log('用户已登录:', userInfo.nickName);
    }

    // 加载本地存储的订阅源和分类
    const feeds = wx.getStorageSync('feeds')
    const categories = wx.getStorageSync('categories')

    console.log('从存储加载的订阅源:', feeds ? feeds.length : 0);
    console.log('从存储加载的分类:', categories ? categories.length : 0);

    if (feeds && Array.isArray(feeds)) {
      this.globalData.feeds = feeds
      console.log('全局订阅源已设置:', feeds.map(f => ({ url: f.url, title: f.title })));
    } else {
      this.globalData.feeds = []
      console.log('没有找到有效的订阅源数据');
    }

    if (categories && Array.isArray(categories)) {
      this.globalData.categories = categories
    } else {
      this.globalData.categories = []
    }
  },

  // 检查登录状态
  checkLoginStatus() {
    return new Promise((resolve, reject) => {
      if (this.globalData.isLoggedIn) {
        resolve(true)
      } else {
        resolve(false)
      }
    })
  },

  // 登录方法
  login() {
    return new Promise((resolve, reject) => {
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: (res) => {
          const userInfo = res.userInfo
          this.globalData.userInfo = userInfo
          this.globalData.isLoggedIn = true
          wx.setStorageSync('userInfo', userInfo)
          resolve(userInfo)
        },
        fail: (err) => {
          reject(err)
        }
      })
    })
  }
}) 
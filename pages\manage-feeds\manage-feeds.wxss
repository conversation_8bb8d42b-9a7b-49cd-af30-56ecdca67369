.container {
  padding: 20rpx;
}

.add-feed-section {
  background: #ffffff;
  padding: 30rpx;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.06);
}

.add-feed-section .input {
  margin-bottom: 20rpx;
}

.category-input {
  display: flex;
  align-items: center;
  margin: 20rpx 0;
}

.picker {
  background: #f5f5f5;
  padding: 16rpx 24rpx;
  border-radius: 4rpx;
  margin-left: 20rpx;
  font-size: 28rpx;
  color: #666666;
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  margin: 20rpx 0;
}

.feeds-list {
  background: #ffffff;
  padding: 24rpx;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
}

.feed-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #e0e0e0;
}

.feed-item:last-child {
  border-bottom: none;
}

.feed-info {
  flex: 1;
}

.feed-title {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 8rpx;
}

.feed-category, .feed-custom-name {
  font-size: 24rpx;
  color: #666666;
  margin-right: 16rpx;
}

.feed-custom-name {
  color: #1890ff;
}

.update-button {
  background: #1890ff;
  color: #ffffff;
  font-size: 24rpx;
  padding: 0 20rpx;
  margin: 0 10rpx 0 0;
}

.delete-button {
  background: #ff5252;
  color: #ffffff;
  font-size: 24rpx;
  padding: 0 20rpx;
  margin: 0;
}

.categories-section {
  background: #ffffff;
  padding: 24rpx;
  border-radius: 8rpx;
}

.category-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.category-item {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  padding: 12rpx 20rpx;
  border-radius: 32rpx;
}

.category-item text {
  font-size: 24rpx;
  color: #666666;
  margin-right: 12rpx;
}

.category-scroll {
  background: #fff;
  white-space: nowrap;
  padding: 20rpx 0 0 0;
  border-bottom: 1rpx solid #e0e0e0;
}

.category-list {
  display: inline-block;
  padding: 0 20rpx;
}

.category-tag {
  display: inline-block;
  padding: 12rpx 32rpx;
  margin-right: 16rpx;
  border-radius: 32rpx;
  background: #f5f5f5;
  color: #666;
  font-size: 26rpx;
  transition: all 0.2s;
}

.category-tag.active {
  background: #1890ff;
  color: #fff;
}

.feed-card {
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);
  margin-bottom: 24rpx;
  padding: 24rpx 20rpx;
  transition: box-shadow 0.2s;
}

.feed-title {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  color: #222;
}

.feed-meta {
  font-size: 24rpx;
  color: #888;
  margin-bottom: 10rpx;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 16rpx;
}

.feed-actions {
  margin-top: 8rpx;
}

.feed-url {
  color: #1890ff;
  word-break: break-all;
}



/* 优化订阅源列表样式 */
.feed-item {
  background: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);
  transition: all 0.2s;
}

.feed-item:active {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.08);
}

.feed-info {
  margin-bottom: 16rpx;
}

.feed-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #222;
  display: block;
  margin-bottom: 8rpx;
}

.feed-url {
  font-size: 24rpx;
  color: #1890ff;
  display: block;
  margin-bottom: 8rpx;
  word-break: break-all;
}

.feed-category {
  font-size: 24rpx;
  color: #666;
  display: block;
}

/* 按钮组样式 */
.feed-item .update-feed-btn,
.feed-item .delete-feed-btn {
  display: inline-block;
  padding: 8rpx 16rpx;
  font-size: 24rpx;
  border-radius: 6rpx;
  margin-right: 12rpx;
  border: none;
  transition: all 0.2s;
}

.update-feed-btn {
  background: #1890ff;
  color: #fff;
}

.update-feed-btn:active {
  background: #096dd9;
}

.delete-feed-btn {
  background: #ff4d4f;
  color: #fff;
}

.delete-feed-btn:active {
  background: #d9363e;
}

/* 主要按钮样式 */
.primary-button {
  background: #007aff;
  color: white;
  border: none;
  padding: 24rpx 48rpx;
  border-radius: 8rpx;
  font-size: 32rpx;
  margin-top: 20rpx;
  width: 100%;
}

.primary-button:disabled {
  background: #cccccc;
  color: #666666;
}

/* 测试按钮样式 */
.test-button {
  background: #ff9500;
  color: white;
  border: none;
  padding: 20rpx 40rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  margin-top: 10rpx;
  width: 100%;
}
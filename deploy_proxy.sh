#!/bin/bash

# RSS代理服务器部署脚本
# 解决编码问题的完整部署方案

echo "开始部署RSS代理服务器..."

# 1. 设置系统编码环境
echo "设置系统编码环境..."
export LANG=zh_CN.UTF-8
export LC_ALL=zh_CN.UTF-8
export LC_CTYPE=zh_CN.UTF-8

# 确保系统支持中文编码
if ! locale -a | grep -q "zh_CN.utf8\|zh_CN.UTF-8"; then
    echo "安装中文语言包..."
    sudo apt-get update
    sudo apt-get install -y language-pack-zh-hans
    sudo locale-gen zh_CN.UTF-8
fi

# 2. 安装Rust（如果未安装）
if ! command -v rustc &> /dev/null; then
    echo "安装Rust..."
    curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
    source ~/.cargo/env
fi

# 3. 创建项目目录
PROJECT_DIR="/opt/rss-proxy"
sudo mkdir -p $PROJECT_DIR
sudo chown $USER:$USER $PROJECT_DIR
cd $PROJECT_DIR

# 4. 复制项目文件（假设文件已上传到服务器）
echo "设置项目文件..."

# 5. 构建项目
echo "构建Rust项目..."
cargo build --release

# 6. 创建systemd服务文件
echo "创建systemd服务..."
sudo tee /etc/systemd/system/rss-proxy.service > /dev/null <<EOF
[Unit]
Description=RSS Proxy Server
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=$PROJECT_DIR
Environment=LANG=zh_CN.UTF-8
Environment=LC_ALL=zh_CN.UTF-8
Environment=LC_CTYPE=zh_CN.UTF-8
Environment=RUST_LOG=info
ExecStart=$PROJECT_DIR/target/release/rss-proxy
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
EOF

# 7. 启动服务
echo "启动服务..."
sudo systemctl daemon-reload
sudo systemctl enable rss-proxy
sudo systemctl start rss-proxy

# 8. 检查服务状态
echo "检查服务状态..."
sudo systemctl status rss-proxy

# 9. 配置防火墙（如果需要）
if command -v ufw &> /dev/null; then
    echo "配置防火墙..."
    sudo ufw allow 3000/tcp
fi

echo "部署完成！"
echo "服务地址: http://your-server-ip:3000"
echo "查看日志: sudo journalctl -u rss-proxy -f"
echo ""
echo "测试命令:"
echo "curl -H 'X-Target-URL: https://rss.keepdev.fun/rss/bilibili/user/dynamic/289706107' http://localhost:3000"

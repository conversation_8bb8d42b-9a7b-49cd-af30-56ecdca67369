import * as md5Module from './md5.js';

// 生成更可靠的唯一ID
export function generateUniqueId(article, feedUrl, index) {
  // 组合多个字段以确保唯一性
  let idComponents = [];
  
  if (article.guid) {
    idComponents.push(article.guid);
  }
  if (article.link) {
    idComponents.push(article.link);
  }
  if (article.title) {
    idComponents.push(article.title);
  }
  if (feedUrl) {
    idComponents.push(feedUrl);
  }
  
  // 如果没有任何有效组件，使用索引和时间戳
  if (idComponents.length === 0) {
    idComponents.push(`fallback_${index}_${Date.now()}`);
  }
  
  const combinedString = idComponents.join('|');
  
  // 使用MD5生成唯一ID
  const md5Function = md5Module.default || md5Module;
  const uniqueId = md5Function(combinedString);
  
  return uniqueId;
}

// 预处理XML字符串，解决编码和格式问题
function preprocessXMLString(xmlString) {
  // 移除乱码字符
  if (xmlString.includes('�')) {
    xmlString = xmlString.replace(/�+/g, '');
  }

  // 处理常见的HTML实体编码问题
  xmlString = xmlString
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'");

  // 清理可能的BOM标记
  if (xmlString.charCodeAt(0) === 0xFEFF) {
    xmlString = xmlString.slice(1);
  }

  return xmlString;
}

// 检测RSS格式类型
function detectFeedType(xmlString) {
  const lowerXml = xmlString.toLowerCase();
  
  if (lowerXml.includes('<feed') && lowerXml.includes('xmlns')) {
    return 'atom';
  } else if (lowerXml.includes('<rss') && lowerXml.includes('version="2.0"')) {
    return 'rss2.0';
  } else if (lowerXml.includes('<rss') && lowerXml.includes('version="1.0"')) {
    return 'rss1.0';
  } else if (lowerXml.includes('<rdf:rdf')) {
    return 'rss1.0';
  } else if (lowerXml.includes('<rss')) {
    return 'rss2.0'; // 默认为RSS 2.0
  } else if (lowerXml.includes('<channel')) {
    return 'rss2.0';
  }
  
  return 'unknown';
}

// 去除 CDATA 并处理编码问题
function stripCDATA(str) {
  if (!str) return '';
  
  // 移除CDATA包装
  let result = str.replace(/<!\[CDATA\[([\s\S]*?)\]\]>/gi, '$1');
  
  // 处理HTML实体编码（包括十六进制和十进制）
  result = result
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'")
    .replace(/&nbsp;/g, ' ')
    // 处理十六进制HTML实体
    .replace(/&#x([0-9A-Fa-f]+);/g, function(_match, hex) {
      return String.fromCharCode(parseInt(hex, 16));
    })
    // 处理十进制HTML实体
    .replace(/&#(\d+);/g, function(_match, dec) {
      return String.fromCharCode(parseInt(dec, 10));
    });
  
  // 修复可能被截断的HTML标签
  result = result
    .replace(/<br\/?\s*$/gi, '<br/>')
    .replace(/<img([^>]*)$/gi, '<img$1>')
    .replace(/<\/?\w+\s*$/gi, '');
  
  // 处理图片标签，添加样式以适应页面宽度
  result = result.replace(/<img([^>]*)>/gi, function(_match, attrs) {
    if (!attrs.includes('style=')) {
      attrs += ' style="max-width: 100%; height: auto; display: block; margin: 10rpx auto;"';
    }
    return `<img${attrs}>`;
  });
  
  // 移除乱码字符
  result = result.replace(/�+/g, '');
  
  return result.trim();
}

// 提取文本内容的通用函数
function getTextContent(content, tagName) {
  const regex = new RegExp(`<${tagName}(?:\\s+[^>]*)?>([\\s\\S]*?)<\\/${tagName}>`, 'i');
  const match = content.match(regex);
  if (match && match[1]) {
    let text = match[1].trim();
    text = stripCDATA(text);
    
    // 对于标题等字段，移除HTML标签
    if (['title', 'author'].includes(tagName)) {
      text = text.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();
    }
    
    return text;
  }
  return '';
}

// 解析RSS 2.0格式
function parseRSS20Feed(xmlString) {
  const articles = [];
  const itemMatches = [...xmlString.matchAll(/<item[^>]*>([\s\S]*?)<\/item>/gi)];
  
  itemMatches.forEach(function(itemMatch, index) {
    const itemContent = itemMatch[1];
    
    const article = {
      title: getTextContent(itemContent, 'title'),
      link: getTextContent(itemContent, 'link'),
      content: getTextContent(itemContent, 'description') || getTextContent(itemContent, 'content:encoded'),
      summary: getTextContent(itemContent, 'description'),
      author: getTextContent(itemContent, 'author') || getTextContent(itemContent, 'dc:creator'),
      pubDate: getTextContent(itemContent, 'pubDate') || getTextContent(itemContent, 'dc:date'),
      guid: getTextContent(itemContent, 'guid'),
      category: [getTextContent(itemContent, 'category')].filter(Boolean)
    };
    
    // 生成唯一ID
    article.id = generateUniqueId(article, 'rss2.0-feed', index);
    
    articles.push(article);
  });
  
  return articles;
}

// 解析Atom格式
function parseAtomFeed(xmlString) {
  const articles = [];
  const entryMatches = [...xmlString.matchAll(/<entry[^>]*>([\s\S]*?)<\/entry>/gi)];
  
  entryMatches.forEach(function(entryMatch, index) {
    const entryContent = entryMatch[1];
    
    // 处理Atom的link标签
    let link = '';
    const linkMatch = entryContent.match(/<link[^>]*href=["']([^"']+)["'][^>]*>/i);
    if (linkMatch) {
      link = linkMatch[1];
    }
    
    // 处理Atom的content标签
    let content = getTextContent(entryContent, 'content') || getTextContent(entryContent, 'summary');
    
    const article = {
      title: getTextContent(entryContent, 'title'),
      link: link,
      content: content,
      summary: getTextContent(entryContent, 'summary'),
      author: getTextContent(entryContent, 'author') || getTextContent(entryContent, 'name'),
      pubDate: getTextContent(entryContent, 'published') || getTextContent(entryContent, 'updated'),
      guid: getTextContent(entryContent, 'id'),
      category: [getTextContent(entryContent, 'category')].filter(Boolean)
    };
    
    // 生成唯一ID
    article.id = generateUniqueId(article, 'atom-feed', index);
    
    articles.push(article);
  });
  
  return articles;
}

// 通用RSS解析函数
function parseGenericFeed(xmlString) {
  // 尝试RSS 2.0格式
  let articles = parseRSS20Feed(xmlString);
  
  // 如果RSS 2.0解析失败，尝试Atom格式
  if (articles.length === 0) {
    articles = parseAtomFeed(xmlString);
  }
  
  return articles;
}

// 主要的RSS解析函数
export function parseRSSFeed(xmlString) {
  try {
    // 处理可能的编码问题
    if (!xmlString || typeof xmlString !== 'string') {
      return [];
    }

    // 预处理XML字符串
    xmlString = preprocessXMLString(xmlString);

    // 检测RSS格式类型
    const feedType = detectFeedType(xmlString);

    let articles = [];
    
    // 根据不同格式进行解析
    switch (feedType) {
      case 'atom':
        articles = parseAtomFeed(xmlString);
        break;
      case 'rss2.0':
      case 'rss1.0':
        articles = parseRSS20Feed(xmlString);
        break;
      default:
        // 尝试通用解析
        articles = parseGenericFeed(xmlString);
        break;
    }

    return articles;
  } catch (err) {
    console.error('RSS解析错误:', err);
    return [];
  }
}

// 日期格式化
export function formatDate(dateString) {
  if (!dateString || dateString === 'Invalid Date') {
    return '未知时间';
  }

  try {
    const date = new Date(dateString);
    
    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      return '未知时间';
    }

    const now = new Date();
    const diff = now.getTime() - date.getTime();

    // 小于1分钟
    if (diff < 60000) {
      return '刚刚';
    }
    // 小于1小时
    if (diff < 3600000) {
      return `${Math.floor(diff / 60000)}分钟前`;
    }
    // 小于24小时
    if (diff < 86400000) {
      return `${Math.floor(diff / 3600000)}小时前`;
    }
    // 小于30天
    if (diff < 2592000000) {
      return `${Math.floor(diff / 86400000)}天前`;
    }

    // 超过30天显示具体日期
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  } catch (err) {
    return '未知时间';
  }
}
